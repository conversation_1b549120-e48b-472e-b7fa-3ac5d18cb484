import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';
import '../utils/image_utils.dart';
import 'pro_image_editor_screen.dart';
import 'post_composition_screen.dart';
import 'video_picker_screen.dart';

class ImagePickerScreen extends StatefulWidget {
  const ImagePickerScreen({super.key});

  @override
  State<ImagePickerScreen> createState() => _ImagePickerScreenState();
}

class _ImagePickerScreenState extends State<ImagePickerScreen> {
  final ImagePicker _picker = ImagePicker();
  bool _isLoading = false;

  /// Save edited image bytes to a temporary file and navigate to post composition
  Future<void> _handleEditedImage(Uint8List bytes) async {
    try {
      // Get temporary directory
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'edited_image_$timestamp.jpg';
      final filePath = path.join(tempDir.path, fileName);

      // Save bytes to file
      final file = File(filePath);
      await file.writeAsBytes(bytes);

      if (mounted) {
        // Navigate to post composition screen
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => PostCompositionScreen(croppedImageFile: file),
          ),
        );
      }
    } catch (e) {
      AppLogger.error('Error saving edited image', error: e);
      if (mounted) {
        _showErrorSnackBar('Failed to save edited image. Please try again.');
      }
    }
  }

  @override
  void initState() {
    super.initState();
    // TODO: Fix permission handler plugin issue
    // _checkPermissions();
  }

  Future<void> _pickImageFromGallery() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 100, // Keep original quality for editing
      );

      if (image != null) {
        final imageFile = File(image.path);

        // Validate the image file
        if (!ImageUtils.isValidImageFile(imageFile)) {
          _showErrorSnackBar('Please select a valid image file');
          return;
        }

        // Check file size (limit to 50MB for processing)
        final fileSizeMB = await ImageUtils.getFileSizeMB(imageFile);
        if (fileSizeMB > 50) {
          _showErrorSnackBar(
            'Image file is too large. Please select an image under 50MB.',
          );
          return;
        }

        // Navigate to image editor
        if (mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder:
                  (context) => ProImageEditorScreen(
                    imageFile: imageFile,
                    onImageEditingComplete: (bytes) async {
                      // Save edited image and navigate to post composition
                      await _handleEditedImage(bytes);
                    },
                  ),
            ),
          );
        }
      }
    } catch (e) {
      AppLogger.error('Error picking image', error: e);
      _showErrorSnackBar('Failed to pick image. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _pickImageFromCamera() async {
    try {
      // Ensure camera permission first
      final camStatus = await Permission.camera.request();
      if (!camStatus.isGranted) {
        _showErrorSnackBar('Camera permission is required to take a photo.');
        return;
      }

      setState(() {
        _isLoading = true;
      });

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 100,
      );

      if (image != null) {
        final imageFile = File(image.path);

        // Navigate to image editor
        if (mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder:
                  (context) => ProImageEditorScreen(
                    imageFile: imageFile,
                    onImageEditingComplete: (bytes) async {
                      // Save edited image and navigate to post composition
                      await _handleEditedImage(bytes);
                    },
                  ),
            ),
          );
        }
      }
    } catch (e) {
      AppLogger.error('Error taking photo', error: e);
      _showErrorSnackBar('Failed to take photo. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _navigateToVideoPickerScreen() {
    if (mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(builder: (context) => const VideoPickerScreen()),
      );
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.darkBlue,
      appBar: AppBar(
        title: const Text(
          'Select Media',
          style: TextStyle(
            color: AppColors.gfGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.darkBlue,
        iconTheme: const IconThemeData(color: AppColors.gfOffWhite),
        elevation: 0,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 40),

              // Instructions
              const Text(
                'Choose how you want to add your media',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppColors.gfOffWhite,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 8),

              const Text(
                'You\'ll be able to edit it in the next step',
                style: TextStyle(fontSize: 14, color: AppColors.gfGrayText),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 60),

              // Gallery Button
              _buildOptionButton(
                icon: Icons.photo_library,
                title: 'Choose from Gallery',
                subtitle: 'Select an existing photo',
                onTap: _isLoading ? null : _pickImageFromGallery,
              ),

              const SizedBox(height: 24),

              // Camera Button
              _buildOptionButton(
                icon: Icons.camera_alt,
                title: 'Take Photo',
                subtitle: 'Use your camera',
                onTap: _isLoading ? null : _pickImageFromCamera,
              ),

              const SizedBox(height: 24),

              // Video Button
              _buildOptionButton(
                icon: Icons.videocam,
                title: 'Add Video',
                subtitle: 'Select or record a video',
                onTap: _isLoading ? null : _navigateToVideoPickerScreen,
              ),

              const Spacer(),

              if (_isLoading)
                const Center(
                  child: CircularProgressIndicator(color: AppColors.gfGreen),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOptionButton({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.gfCardBackground,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppColors.gfGrayBorder, width: 1),
        ),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: AppColors.gfDarkBackground40,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: AppColors.gfGreen, size: 32),
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: AppColors.gfOffWhite,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.gfGrayText,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              color: AppColors.gfGrayText,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
