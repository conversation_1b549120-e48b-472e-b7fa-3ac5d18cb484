import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import '../theme/app_theme.dart';
import '../screens/video_editor_screen.dart';
import '../screens/post_composition_screen.dart';
import '../utils/app_logger.dart';
import '../utils/video_utils.dart';

class VideoPickerScreen extends StatefulWidget {
  const VideoPickerScreen({super.key});

  @override
  State<VideoPickerScreen> createState() => _VideoPickerScreenState();
}

class _VideoPickerScreenState extends State<VideoPickerScreen> {
  final ImagePicker _picker = ImagePicker();
  bool _isLoading = false;

  /// Save edited video file and navigate to post composition
  Future<void> _handleEditedVideo(File videoFile) async {
    try {
      if (mounted) {
        // Navigate to post composition screen
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder:
                (context) => PostCompositionScreen(
                  croppedImageFile: null,
                  videoFile: videoFile,
                ),
          ),
        );
      }
    } catch (e) {
      AppLogger.error('Error handling edited video', error: e);
      _showErrorSnackBar('Failed to process edited video. Please try again.');
    }
  }

  Future<bool> _ensurePhotosPermissionForVideoGallery() async {
    // iOS: require Photos permission. Android: no pre-request; system picker handles access.
    if (Platform.isIOS) {
      var photos = await Permission.photos.status;
      if (photos.isGranted || photos.isLimited) return true;

      photos = await Permission.photos.request();
      if (photos.isGranted || photos.isLimited) return true;

      if (photos.isPermanentlyDenied) {
        await _showPermissionDialog(
          title: 'Photos Permission Needed',
          message:
              'Photos permission is required to select videos from your gallery. Please enable it in Settings.',
        );
      } else {
        _showErrorSnackBar('Photos permission is required to select videos.');
      }
      return false;
    }
    return true;
  }

  Future<void> _pickVideoFromGallery() async {
    try {
      final ok = await _ensurePhotosPermissionForVideoGallery();
      if (!ok) return;

      setState(() {
        _isLoading = true;
      });

      final XFile? video = await _picker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(seconds: 30), // Limit to 30 seconds
      );

      if (video != null) {
        final videoFile = File(video.path);

        // Validate the video file
        if (!VideoUtils.isValidVideoFile(videoFile)) {
          _showErrorSnackBar('Please select a valid video file');
          return;
        }

        // Check file size (limit to 30MB for 30-second videos)
        final fileSizeMB = await VideoUtils.getFileSizeMB(videoFile);
        if (fileSizeMB > 30) {
          _showErrorSnackBar(
            'Video file is too large. Please select a video under 30MB.',
          );
          return;
        }

        // Navigate to video editor
        if (mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder:
                  (context) => VideoEditorScreen(
                    videoFile: videoFile,
                    onVideoEditingComplete: (editedVideoFile) async {
                      // Save edited video and navigate to post composition
                      await _handleEditedVideo(editedVideoFile);
                    },
                  ),
            ),
          );
        }
      }
    } catch (e) {
      AppLogger.error('Error picking video from gallery', error: e);
      _showErrorSnackBar('Failed to pick video. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<bool> _ensureCameraAndMicrophonePermissions() async {
    var c = await Permission.camera.status;
    var m = await Permission.microphone.status;

    if (c.isGranted && m.isGranted) return true;

    if (!c.isGranted) c = await Permission.camera.request();
    if (!m.isGranted) m = await Permission.microphone.request();

    if (c.isGranted && m.isGranted) return true;

    if (c.isPermanentlyDenied || m.isPermanentlyDenied) {
      final missing = [
        if (c.isPermanentlyDenied) 'Camera',
        if (m.isPermanentlyDenied) 'Microphone',
      ].join(' and ');
      await _showPermissionDialog(
        title: 'Permissions Needed',
        message:
            '$missing permission(s) are required to record video. Please enable in Settings.',
      );
    } else {
      final missing = [
        if (!c.isGranted) 'Camera',
        if (!m.isGranted) 'Microphone',
      ].join(' and ');
      _showErrorSnackBar(
        '$missing permission(s) are required to record video.',
      );
    }
    return false;
  }

  Future<void> _pickVideoFromCamera() async {
    try {
      final ok = await _ensureCameraAndMicrophonePermissions();
      if (!ok) return;

      setState(() {
        _isLoading = true;
      });

      final XFile? video = await _picker.pickVideo(
        source: ImageSource.camera,
        maxDuration: const Duration(seconds: 30), // Limit to 30 seconds
      );

      if (video != null) {
        final videoFile = File(video.path);

        // Navigate to video editor
        if (mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder:
                  (context) => VideoEditorScreen(
                    videoFile: videoFile,
                    onVideoEditingComplete: (editedVideoFile) async {
                      // Save edited video and navigate to post composition
                      await _handleEditedVideo(editedVideoFile);
                    },
                  ),
            ),
          );
        }
      }
    } catch (e) {
      AppLogger.error('Error recording video', error: e);
      _showErrorSnackBar('Failed to record video. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: Colors.red),
      );
    }
  }

  Future<void> _showPermissionDialog({
    required String title,
    required String message,
  }) async {
    if (!mounted) return;
    await showDialog<void>(
      context: context,
      builder:
          (ctx) => AlertDialog(
            title: Text(title),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(ctx).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.of(ctx).pop();
                  await openAppSettings();
                },
                child: const Text('Open Settings'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.darkBlue,
      appBar: AppBar(
        title: const Text(
          'Select Video',
          style: TextStyle(
            color: AppColors.gfGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.darkBlue,
        iconTheme: const IconThemeData(color: AppColors.gfOffWhite),
        elevation: 0,
      ),
      body:
          _isLoading
              ? const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
                ),
              )
              : Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.videocam,
                      size: 80,
                      color: AppColors.gfGreen,
                    ),
                    const SizedBox(height: 32),
                    const Text(
                      'Choose how to add your video',
                      style: TextStyle(
                        color: AppColors.gfOffWhite,
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 48),

                    // Gallery button
                    SizedBox(
                      width: double.infinity,
                      height: 56,
                      child: ElevatedButton.icon(
                        onPressed: _pickVideoFromGallery,
                        icon: const Icon(
                          Icons.video_library,
                          color: AppColors.darkBlue,
                        ),
                        label: const Text(
                          'Choose from Gallery',
                          style: TextStyle(
                            color: AppColors.darkBlue,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.gfGreen,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Camera button
                    SizedBox(
                      width: double.infinity,
                      height: 56,
                      child: OutlinedButton.icon(
                        onPressed: _pickVideoFromCamera,
                        icon: const Icon(
                          Icons.videocam,
                          color: AppColors.gfGreen,
                        ),
                        label: const Text(
                          'Record Video',
                          style: TextStyle(
                            color: AppColors.gfGreen,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(
                            color: AppColors.gfGreen,
                            width: 2,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 32),

                    const Text(
                      'Videos are limited to 30 seconds and 30MB',
                      style: TextStyle(
                        color: AppColors.gfGrayText,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
    );
  }
}
