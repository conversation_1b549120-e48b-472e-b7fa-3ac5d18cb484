import 'package:flutter/material.dart';
import '../components/index.dart';
import '../models/post_model.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';
import '../screens/user_profile_screen.dart';
import '../services/engagement_tracking_service.dart';

class FeedItem extends StatefulWidget {
  final PostModel post;
  final bool isVisible;

  const FeedItem({super.key, required this.post, this.isVisible = true});

  @override
  State<FeedItem> createState() => _FeedItemState();
}

class _FeedItemState extends State<FeedItem> {
  // Cache media URL to avoid repeated FutureBuilder calls
  Future<String?>? _mediaUrlFuture;

  @override
  void didUpdateWidget(FeedItem oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Handle view tracking when visibility changes
    if (oldWidget.isVisible != widget.isVisible) {
      if (widget.isVisible) {
        // Post became visible - start tracking
        EngagementTrackingService.instance.startViewTracking(widget.post);
      } else {
        // Post became invisible - end tracking
        EngagementTrackingService.instance.endViewTracking(oldWidget.post.id);
      }
    }

    // Only clear cache if the post ID actually changed (different post)
    if (oldWidget.post.id != widget.post.id) {
      // End tracking for old post if it was visible
      if (oldWidget.isVisible) {
        EngagementTrackingService.instance.endViewTracking(oldWidget.post.id);
      }

      _mediaUrlFuture = null;

      // Start tracking for new post if it's visible
      if (widget.isVisible) {
        EngagementTrackingService.instance.startViewTracking(widget.post);
      }
    }
  }

  @override
  void initState() {
    super.initState();
    // Initialize media URL future on first build
    if (widget.post.hasMedia) {
      _mediaUrlFuture = widget.post.getMediaUrl();
    }

    // Start view tracking if this post is initially visible
    if (widget.isVisible) {
      EngagementTrackingService.instance.startViewTracking(widget.post);
    }
  }

  @override
  void dispose() {
    // End view tracking when widget is disposed
    if (widget.isVisible) {
      EngagementTrackingService.instance.endViewTracking(widget.post.id);
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        children: [
          // Media content (image or video) - positioned to fill the entire area
          Positioned.fill(child: IgnorePointer(child: _buildMediaContent())),

          // Top overlay - User info and post content - positioned above media
          GFFeedTopOverlay(
            key: ValueKey('top_overlay_${widget.post.id}'),
            post: widget.post,
            onUserTap: () => _navigateToUserProfile(context),
          ),

          // Bottom overlay - Action buttons - positioned above media
          GFFeedBottomOverlay(
            key: ValueKey('bottom_overlay_${widget.post.id}'),
            post: widget.post,
          ),
        ],
      ),
    );
  }

  void _navigateToUserProfile(BuildContext context) {
    AppLogger.debug(
      'FeedItem: Avatar/User tapped! Navigating to user profile for user ${widget.post.userId}',
    );

    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => UserProfileScreen(
              userId: widget.post.userId,
              username: widget.post.username,
            ),
      ),
    );
  }

  Widget _buildMediaContent() {
    if (!widget.post.hasMedia) {
      return _buildMediaPlaceholder('No Media');
    }

    // Use cached future to avoid repeated calls
    _mediaUrlFuture ??= widget.post.getMediaUrl();

    return FutureBuilder<String?>(
      future: _mediaUrlFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: GFLoadingIndicator());
        }

        final mediaUrl = snapshot.data;
        if (mediaUrl != null) {
          if (widget.post.isImage) {
            return GFLazyImage(
              key: ValueKey('image_${widget.post.id}_$mediaUrl'),
              imageUrl: mediaUrl,
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.fitWidth,
              onTap: () {
                // Handle image tap if needed
              },
              errorWidget: _buildMediaPlaceholder('Image'),
            );
          } else if (widget.post.isVideo) {
            // Build video player fresh so visibility/autoPlay updates correctly.
            // Video data itself is cached by GFVideoPlayer's controller cache.
            return GFVideoPlayer(
              key: ValueKey('video_${widget.post.id}_$mediaUrl'),
              videoUrl: mediaUrl,
              autoPlay: widget.isVisible,
              showControls: true,
            );
          }
        }

        // Fallback to placeholder
        return _buildMediaPlaceholder('Media');
      },
    );
  }

  Widget _buildMediaPlaceholder(String type) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.gfTeal, AppColors.gfDarkBlue],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              type == 'Video' ? Icons.play_circle_outline : Icons.image,
              size: 64,
              color: Colors.white70,
            ),
            const SizedBox(height: 16),
            Text(
              '$type Content',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
