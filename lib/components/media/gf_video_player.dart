import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import '../../theme/app_theme.dart';
import '../core/gf_loading.dart';
import '../../utils/app_logger.dart';
import '../../services/media_preloader.dart';

// Global video controller cache to prevent reloading
class _VideoControllerCache {
  static final Map<String, VideoPlayerController> _controllers = {};

  static VideoPlayerController? getController(String url) {
    return _controllers[url];
  }

  static void setController(String url, VideoPlayerController controller) {
    _controllers[url] = controller;
  }
}

/// GameFlex video player component
///
/// A reusable video player component that provides consistent video playback
/// functionality across the app with GameFlex styling.
///
/// Example usage:
/// ```dart
/// GFVideoPlayer(
///   videoUrl: post.videoUrl,
///   aspectRatio: 16/9,
///   autoPlay: true,
///   showControls: true,
/// )
/// ```
class GFVideoPlayer extends StatefulWidget {
  final String? videoUrl;
  final double? aspectRatio;
  final bool autoPlay;
  final bool loop;
  final bool muted;
  final bool showControls;
  final bool showMuteButton;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BorderRadius? borderRadius;

  const GFVideoPlayer({
    super.key,
    this.videoUrl,
    this.aspectRatio,
    this.autoPlay = false,
    this.loop = true,
    this.muted = false,
    this.showControls = true,
    this.showMuteButton = true,
    this.onTap,
    this.onLongPress,
    this.placeholder,
    this.errorWidget,
    this.borderRadius,
  });

  @override
  State<GFVideoPlayer> createState() => _GFVideoPlayerState();
}

class _GFVideoPlayerState extends State<GFVideoPlayer> {
  VideoPlayerController? _controller;
  bool _isPlaying = false;
  bool _isMuted = true;
  final bool _showControls = false;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _isPlaying = widget.autoPlay;
    _isMuted = widget.muted;
    _initializeVideo();
  }

  @override
  void didUpdateWidget(GFVideoPlayer oldWidget) {
    super.didUpdateWidget(oldWidget);

    AppLogger.debug(
      'GFVideoPlayer: didUpdateWidget called - oldURL: ${oldWidget.videoUrl}, newURL: ${widget.videoUrl}',
    );

    // Only reinitialize if the video URL actually changed
    if (oldWidget.videoUrl != widget.videoUrl) {
      AppLogger.debug('GFVideoPlayer: Video URL changed, reinitializing');
      _controller?.dispose();
      _controller = null;
      _initializeVideo();
    } else {
      AppLogger.debug(
        'GFVideoPlayer: Widget updated but video URL unchanged, keeping existing player',
      );
      // Update other properties that might have changed without reinitializing
      if (oldWidget.autoPlay != widget.autoPlay && _controller != null) {
        if (widget.autoPlay && !_isPlaying) {
          _togglePlayPause();
        } else if (!widget.autoPlay && _isPlaying) {
          _togglePlayPause();
        }
      }
      if (oldWidget.muted != widget.muted && _controller != null) {
        _isMuted = widget.muted;
        _controller!.setVolume(_isMuted ? 0.0 : 1.0);
      }
    }
  }

  @override
  void dispose() {
    // Don't dispose cached controllers - they're managed by the cache
    if (_controller != null && widget.videoUrl != null) {
      final cachedController = _VideoControllerCache.getController(
        widget.videoUrl!,
      );
      if (cachedController != _controller) {
        // Only dispose if it's not the cached controller
        _controller?.dispose();
      }
    }
    super.dispose();
  }

  void _initializeVideo() async {
    if (widget.videoUrl == null || widget.videoUrl!.isEmpty) {
      setState(() {
        _isLoading = false;
      });
      return;
    }

    // Prevent multiple simultaneous initializations
    if (_controller != null && _controller!.value.isInitialized) {
      AppLogger.debug(
        'GFVideoPlayer: Controller already initialized, skipping',
      );
      return;
    }

    try {
      AppLogger.debug('GFVideoPlayer: Initializing video ${widget.videoUrl}');

      // Check if we have a cached controller first
      final cachedController = _VideoControllerCache.getController(
        widget.videoUrl!,
      );
      if (cachedController != null && cachedController.value.isInitialized) {
        AppLogger.debug(
          'GFVideoPlayer: Using cached controller - video should not reload!',
        );
        _controller = cachedController;
        setState(() {
          _isLoading = false;
          _hasError = false;
          _isPlaying = widget.autoPlay;
        });

        // Apply initial settings
        _controller!.setVolume(_isMuted ? 0.0 : 1.0);
        if (widget.autoPlay && !_controller!.value.isPlaying) {
          _controller!.play();
        }
        return;
      }

      // Check if we have a preloaded controller
      final preloadedController = MediaPreloader.instance
          .getPreloadedVideoController(widget.videoUrl!);

      if (preloadedController != null) {
        AppLogger.debug('GFVideoPlayer: Using preloaded controller');
        _controller = preloadedController;
      } else {
        AppLogger.debug('GFVideoPlayer: Creating new controller');
        // Check if it's a network URL or local file
        if (widget.videoUrl!.startsWith('http')) {
          _controller = VideoPlayerController.networkUrl(
            Uri.parse(widget.videoUrl!),
            videoPlayerOptions: VideoPlayerOptions(
              mixWithOthers: true,
              allowBackgroundPlayback: false,
            ),
          );
        } else {
          _controller = VideoPlayerController.asset(widget.videoUrl!);
        }

        await _controller!.initialize();

        // Cache the newly created controller
        _VideoControllerCache.setController(widget.videoUrl!, _controller!);
      }

      if (mounted) {
        await _controller!.setLooping(widget.loop);

        final initialVolume = _isMuted ? 0.0 : 1.0;
        AppLogger.debug(
          'GFVideoPlayer: Setting initial volume to $initialVolume (muted: $_isMuted)',
        );
        await _controller!.setVolume(initialVolume);

        if (widget.autoPlay) {
          await _controller!.play();
          _isPlaying = true;
        }

        setState(() {
          _isLoading = false;
        });

        AppLogger.debug('GFVideoPlayer: Video initialized successfully');
      }
    } catch (e) {
      AppLogger.error('GFVideoPlayer: Failed to initialize video', error: e);
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget videoWidget = _buildVideoContent();

    // Use provided aspect ratio or video's natural aspect ratio
    double? aspectRatio = widget.aspectRatio;
    if (aspectRatio == null &&
        _controller != null &&
        _controller!.value.isInitialized) {
      final size = _controller!.value.size;
      if (size.width > 0 && size.height > 0) {
        aspectRatio = size.width / size.height;
      }
    }

    if (aspectRatio != null) {
      videoWidget = AspectRatio(aspectRatio: aspectRatio, child: videoWidget);
    }

    return ClipRRect(
      borderRadius: widget.borderRadius ?? BorderRadius.zero,
      child: videoWidget,
    );
  }

  Widget _buildVideoContent() {
    if (widget.videoUrl == null || widget.videoUrl!.isEmpty) {
      return _buildPlaceholder();
    }

    if (_hasError) {
      return _buildErrorState();
    }

    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_controller == null || !_controller!.value.isInitialized) {
      return _buildLoadingState();
    }

    return GestureDetector(
      onTap: _handleTap,
      onLongPress: widget.onLongPress,
      child: Stack(
        children: [
          // Actual video player - use FittedBox to prevent stretching
          Center(
            child: FittedBox(
              fit: BoxFit.contain,
              child: SizedBox(
                width: _controller!.value.size.width,
                height: _controller!.value.size.height,
                child: VideoPlayer(_controller!),
              ),
            ),
          ),

          // Controls overlay
          if (_showControls && widget.showControls) _buildControlsOverlay(),

          // Mute button (always visible when enabled)
          if (widget.showMuteButton && !_showControls)
            Positioned(top: 16, right: 16, child: _buildMuteButton()),
        ],
      ),
    );
  }

  Widget _buildPlaceholder() {
    if (widget.placeholder != null) return widget.placeholder!;

    return Container(
      color: AppColors.gfDarkBackground,
      child: const Center(
        child: Icon(
          Icons.videocam_outlined,
          color: AppColors.gfGrayText,
          size: 48,
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      color: Colors.black,
      child: const Center(child: GFLoadingIndicator(color: Colors.white)),
    );
  }

  Widget _buildErrorState() {
    if (widget.errorWidget != null) return widget.errorWidget!;

    return Container(
      color: AppColors.gfDarkBackground,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: AppColors.gfGrayText, size: 48),
            SizedBox(height: 8),
            Text(
              'Failed to load video',
              style: TextStyle(color: AppColors.gfGrayText, fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlsOverlay() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withValues(alpha: 102), // 0.4 opacity
            Colors.transparent,
            Colors.black.withValues(alpha: 102), // 0.4 opacity
          ],
        ),
      ),
      child: Stack(
        children: [
          // Play/Pause button
          Center(
            child: GestureDetector(
              onTap: _togglePlayPause,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 128), // 0.5 opacity
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _isPlaying ? Icons.pause : Icons.play_arrow,
                  color: Colors.white,
                  size: 32,
                ),
              ),
            ),
          ),

          // Mute button
          if (widget.showMuteButton)
            Positioned(bottom: 16, right: 16, child: _buildMuteButton()),
        ],
      ),
    );
  }

  Widget _buildMuteButton() {
    return GestureDetector(
      onTap: () {
        AppLogger.debug('GFVideoPlayer: Mute button tapped');
        _toggleMute();
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        // Larger touch area for better responsiveness
        padding: const EdgeInsets.all(12),
        child: Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 128), // 0.5 opacity
            shape: BoxShape.circle,
          ),
          child: Icon(
            _isMuted ? Icons.volume_off : Icons.volume_up,
            color: Colors.white,
            size: 18,
          ),
        ),
      ),
    );
  }

  void _handleTap() {
    if (widget.onTap != null) {
      widget.onTap!();
    } else {
      // Default behavior: toggle play/pause
      _togglePlayPause();
    }
  }

  void _togglePlayPause() async {
    if (_controller == null) return;

    try {
      if (_isPlaying) {
        await _controller!.pause();
      } else {
        await _controller!.play();
      }

      setState(() {
        _isPlaying = !_isPlaying;
      });
    } catch (e) {
      AppLogger.error('GFVideoPlayer: Failed to toggle play/pause', error: e);
    }
  }

  void _toggleMute() async {
    if (_controller == null) {
      AppLogger.debug('GFVideoPlayer: Cannot toggle mute - controller is null');
      return;
    }

    try {
      final newVolume = _isMuted ? 1.0 : 0.0;
      AppLogger.debug(
        'GFVideoPlayer: Setting volume to $newVolume (was muted: $_isMuted)',
      );
      await _controller!.setVolume(newVolume);

      setState(() {
        _isMuted = !_isMuted;
      });

      AppLogger.debug(
        'GFVideoPlayer: Mute toggled successfully (now muted: $_isMuted)',
      );
    } catch (e) {
      AppLogger.error('GFVideoPlayer: Failed to toggle mute', error: e);
    }
  }
}

/// Simplified video player for thumbnails
class GFVideoThumbnail extends StatelessWidget {
  final String? videoUrl;
  final String? thumbnailUrl;
  final double? width;
  final double? height;
  final double? aspectRatio;
  final VoidCallback? onTap;
  final BorderRadius? borderRadius;
  final bool showPlayIcon;

  const GFVideoThumbnail({
    super.key,
    this.videoUrl,
    this.thumbnailUrl,
    this.width,
    this.height,
    this.aspectRatio,
    this.onTap,
    this.borderRadius,
    this.showPlayIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    Widget thumbnail = Stack(
      fit: StackFit.expand,
      children: [
        // Thumbnail image
        Container(
          color: AppColors.gfDarkBackground,
          child:
              thumbnailUrl != null && thumbnailUrl!.isNotEmpty
                  ? Image.network(
                    thumbnailUrl!,
                    fit: BoxFit.cover,
                    errorBuilder:
                        (context, error, stackTrace) => _buildPlaceholder(),
                  )
                  : _buildPlaceholder(),
        ),

        // Play icon overlay
        if (showPlayIcon)
          Center(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 128), // 0.5 opacity
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.play_arrow,
                color: Colors.white,
                size: 32,
              ),
            ),
          ),
      ],
    );

    if (aspectRatio != null) {
      thumbnail = AspectRatio(aspectRatio: aspectRatio!, child: thumbnail);
    }

    if (width != null || height != null) {
      thumbnail = SizedBox(width: width, height: height, child: thumbnail);
    }

    if (onTap != null) {
      thumbnail = GestureDetector(onTap: onTap, child: thumbnail);
    }

    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.zero,
      child: thumbnail,
    );
  }

  Widget _buildPlaceholder() {
    return const Center(
      child: Icon(
        Icons.videocam_outlined,
        color: AppColors.gfGrayText,
        size: 48,
      ),
    );
  }
}
